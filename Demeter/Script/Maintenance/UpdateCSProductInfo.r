suppressMessages(library(Demeter))


componentName = "UpdateCSProductInfo"
InitLogger(GenLogPath("~/Log", componentName))

SetPreferredConcurrency()
InitDemeter()

rawDataPath = "~/RawData/MarketData"
standardCSVPath = file.path(rawDataPath, "Standard")
fstDataPath = "~/Data/MarketData"
rq = GetRQDataSource()
rq$rq$init()
standardCSV = GetCSVDataSource(standardCSVPath)
fstData = GetFSTDataSource(fstDataPath)


# step 1: get raw data of each type from RQ
type = c("CS", "INDX", "ETF", "LOF", "Convertible", "Repo")
marketType = "ChinaStock"

for (t in type) {
    rqStockInfo = rq$rq$all_instruments(type = t)
    stockInfo = Demeter:::ParsePyDf(rqStockInfo)
    if(IsEmpty(stockInfo)) {
        WarningLog("", "Failed to get {t} from RQ")
    }
    setnames(stockInfo, old = "symbol", new = "cnsymbol")
    timestamp = GetBeginOfTradingDay(GetNow(), fstData, marketType)
    stockInfo[, c("timestamp", "symbol") := list(timestamp, file_path_sans_ext(order_book_id))]
    stockInfo[, c("index", "order_book_id") := NULL]
    stockInfo[, symbol := RQSymbolToStandard(t, symbol)]
    setcolorder(stockInfo, c("timestamp", "symbol"))
    convertedNames = gsub("_", "", names(stockInfo))
    setnames(stockInfo, convertedNames)
    convertNaTAndNUll = function(x) {
        if (is.list(x)) x = unlist(sapply(x, function(v) return(ifelse(is.null(v), "", v))))
        return(x)
    }
    stockInfo[, names(stockInfo) := lapply(.SD, convertNaTAndNUll), .SDcols = names(stockInfo)]
    stockInfo[, timestamp := TimeToStr(timestamp)]
    infoPath = file.path(rawDataPath, "RQRaw", paste0(t, ".csv"))
    dir.create(dirname(infoPath), showWarnings = FALSE)
    InfoLog(componentName, "Writting market info to: {infoPath}")
    fwrite(stockInfo, infoPath)
}

# step 2: copy market info of each type to data source

ConvertRQMarketType = function(type) {
    return(switch(type,
        CS = "ChinaStock",
        ETF = "ChinaETF",
        Convertible = "ChinaCvtBond",
        Repo = "ChinaPlgBond",
        LOF = "ChinaFund"
    ))
}

ReadCSProductInfo = function(marketType, path) {
    tz = GetTimezone(marketType)
    timediff = GetTimediff(marketType)
    colClass = list(character = c("symbol", "listeddate", "delisteddate"))
    data = Demeter:::CSVReadFunc(path, tz, timediff, colClass = colClass, convertTimeColumn = TRUE)
    data[delisteddate == "0000-00-00", delisteddate := NA]
    data[listeddate == "0000-00-00", listeddate := NA]
    data[listeddate == "2999-12-31", listeddate := NA]
    data[, listeddate := MakeTime(listeddate, tz)]
    data[, delisteddate := MakeTime(delisteddate, tz)]
    return(data)
}
# init

type = c("CS", "ETF", "LOF", "Convertible", "Repo")
for (t in type) {
    path = file.path(rawDataPath, "RQRaw", paste0(t, ".csv"))

    marketType = ConvertRQMarketType(t)
    dataType = "Misc/ProductInfo"
    data = ReadCSProductInfo(marketType, path)
    if (t == "CS") {
        indexPath = file.path(rawDataPath, "RQRaw", paste0("INDX", ".csv"))
        indexData = ReadCSProductInfo(marketType, indexPath)
        data = rbind(data, indexData, fill = TRUE)
    }

    setattr(data, "marketType", marketType)
    setattr(data, "dataType", dataType)
    RemoveDataAfter(fstDataPath, marketType, dataType, needConfirm = FALSE)
    fstData$WriteData(data)
}
