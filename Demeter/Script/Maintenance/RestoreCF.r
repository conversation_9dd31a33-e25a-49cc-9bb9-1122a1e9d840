suppressMessages(library(Demeter))
options(warn = 1, showWarnCalls = TRUE)


componentName = "RestoreCF"
logPath = GenLogPath("~/Log", componentName)
InitLogger(logPath)

InfoLog(componentName, "Restore cf data start")

SetPreferredConcurrency()
InitDemeter()



# init
DefaultTimezone = Demeter::GetTimezone("ChinaFuture")

args = ProcessCmdArgs(c(),list(begin = "2020-01-01", CopyDataFlag = "FALSE"))
if (is.null(args)) quit()

begin = args$beginCSVpyDataFlag = as.logical(args$CopyDataFlag)

serverName = GetHostName()

rawDataRoot = "~/RawData/MarketData"
fstDataPath = "~/Data/MarketData"
archivePath = "~/Archive/MarketData"

mdsh = GetMDSH(GetMDSHFilePath("ChinaFuture"))
standardCSVPath = file.path(rawDataRoot, "Standard")
standardArchivePath = file.path(archivePath, "Standard")
tempCSVPath = file.path(rawDataRoot, "TempCSV")


standardCSV = GetCSVDataSource(standardCSVPath)
fstData = GetFSTDataSource(fstDataPath)
tempCSV = GetCSVDataSource(tempCSVPath)


if (!is.null(begin)) {
    begin = GetTradingDay(begin, fstData, "ChinaFuture")
}


# restore mdsh data
RestoreMDSHData(mdsh, "ChinaFuture", begin = begin)

# restore standard

RestoreFromArchive(standardArchivePath, standardCSVPath, "ChinaFuture", "Tick", "xz", begin)
RestoreFromArchive(standardArchivePath, standardCSVPath, "ChinaFuture", "Orderbook5", "xz", begin)

RestoreFromArchive(standardArchivePath, standardCSVPath, "ChinaOption", "Tick", "xz", begin)
RestoreFromArchive(standardArchivePath, standardCSVPath, "ChinaOption", "Orderbook5", "xz", begin)

# restore Misc and Bar
RestoreFromArchive(standardArchivePath, tempCSVPath, "ChinaFuture", "Misc", "xz")
RestoreFromArchive(standardArchivePath, tempCSVPath, "ChinaOption", "Misc", "xz")
RestoreFromArchive(standardArchivePath, tempCSVPath, "ChinaFuture", "Bar/60", "xz", begin)


if (!is.null(CopyDataFlag) && CopyDataFlag) {
    # copy restore data from standard to fst
    CopyData("ChinaFuture", "Tick", standardCSV, fstData)
    CopyData("ChinaFuture", "Orderbook5", standardCSV, fstData)

    CopyData("ChinaOption", "Tick", standardCSV, fstData)
    CopyData("ChinaOption", "Orderbook5", standardCSV, fstData)


    # copy restore data from tempCSV to fst

    cfMiscDataType = paste0("Misc/", c("Margin", "Commission", "ExchangeRebate", "TradingDay", "Session", "PxLimit", "Major", "OIMajor"))
    for (dataType in cfMiscDataType) {
        CopyData("ChinaFuture", dataType, tempCSV, fstData)
    }

    CopyData("ChinaFuture", "Bar/60", tempCSV, fstData)


    coMiscDataType = paste0("Misc/", c("TradingDay", "Session", "PxLimit"))
    for (dataType in coMiscDataType) {
        CopyData("ChinaOption", dataType, tempCSV, fstData)
    }
}

warnings()
InfoLog(componentName, "Restore cf data done")