suppressMessages(library(Demeter))
suppressMessages(library(Venus))


componentName = "UpdateExchangeRebate"
InitLogger(GenLogPath("~/Log", componentName))
SetPreferredConcurrency()
InitDemeter()
args = ProcessCmdArgs(NULL, list(server = GetDataServer(), deployToServer = FALSE, rawDataRoot = "~/VMHome/RawData/MarketData/ExchRaw/ChinaFuture/ExchRebate/"))
deployToServer = as.logical(args$deployToServer)
rawDataRoot = args$rawDataRoot
localExchangeRebateFile = "~/projects/Aegis/Demeter/Data/ExchangeRebate.csv"
currentExchangeRebateFile = "~/RawData/MarketData/Standard/ChinaFuture/Misc/ExchangeRebate.csv"
ds = GetFSTDataSource("~/Data/MarketData")

if (!deployToServer) {
    GenExchangeRebate(rawDataRoot, localExchangeRebateFile, ds)
    diffData = GetExchangeRebateDiffData(localExchangeRebateFile, currentExchangeRebateFile)
    DrawDataTable(diffData$data, highlightCols = diffData$col)
    isSame = CompareFiles(currentExchangeRebateFile, localExchangeRebateFile)
    InfoLog("UpdateLocalExchangeRebate", "ExchangeRebate {ifelse(isSame, 'unchanged', 'changed')}")
} else {
    rawExchangeRebatePath = "~/RawData/MarketData/ExchRaw/ChinaFuture/ExchRebate/"
    rsyncCmd = glue("rsync -air {rawDataRoot} dba@server:{rawExchangeRebatePath}")
    if (!endsWith(rawDataRoot, "/")) {
        WarningLog("UpdateExchangeRebate", 'Warning! rawDataRoot should end with "/"')
    }
    if (!Confirm(glue('Are you sure to continue exce: "{rsyncCmd}"?'))) {
        quit()
    }
    sapply(args$server, \(s) BashExec(glue("rsync -air {rawDataRoot} dba@{s}:{rawExchangeRebatePath}")))
    updateServer = paste0(args$server, collapse = ",")
    cmd = glue("Rscript ~/projects/Aegis/Demeter/Script/Maintenance/UpdateDataServerMiscData.r marketType=ChinaFuture dataType=ExchangeRebate server={updateServer}")
    BashExec(cmd)
}