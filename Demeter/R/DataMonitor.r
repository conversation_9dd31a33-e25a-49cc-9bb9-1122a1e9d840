#' nodoc
#' @export
GetMDOrder = function(solidDataRoot, marketType, solidDataType, monitorDay, derivName = NULL) {
    filePath = file.path(solidDataRoot, marketType, solidDataType, paste0(DateToStr(monitorDay), ".csv"))
    if (!file.exists(filePath)) return(NULL)
    data = fread(filePath, header = TRUE, integer64 = "numeric", colClasses = c(symbol = "character"), tz = "")
    if (IsEmpty(data)) return(NULL)

    data[, derivName := GetDerivNameOfSymbol(marketType, symbol)]
    if (is.null(derivName)) derivName = unique(data$derivName)

    checkSameOrder = function(v1, v2) {
        m = match(v1, v2)
        return(!any(is.na(m)) && !is.unsorted(m))
    }

    ret = data.table()
    for (der in derivName) {
        derData = data[derivName == der]
        orderData = derData[, .(order = paste0(unique(symbol), collapse = " ")), by = exchtime]
        t <- table(orderData$order)
        t = t / sum(t)

        orderTable = data.table(derivname = der, mdOrder = names(t), percentage = as.numeric(t))
        orderTable[, mdOrder := str_split(mdOrder, " ")]
        orderTable[, orderLength := sapply(orderTable$mdOrder, length)]
        setorder(orderTable, - "orderLength", - "percentage")
        orderLengthVec = unique(orderTable$orderLength)
        candiOrder = data.table()
        for (ol in orderLengthVec) {
            # get the top 3 of each order length
            maxIdx = min(nrow(orderTable[orderLength == ol]), 3)
            candiOrder = rbind(candiOrder, orderTable[orderLength == ol][1:maxIdx])
        }

        uniqueOrder = candiOrder[orderLength == first(orderLengthVec)]
        remainOrderLength = orderLengthVec[-1]

        if (length(remainOrderLength) > 0) {
            for (i in remainOrderLength) {
                candidateIdx = which(candiOrder$orderLength == i)
                for (idx in candidateIdx) {
                    matchResult = FALSE
                    for (j in 1:nrow(uniqueOrder)) {
                        same = checkSameOrder(candiOrder$mdOrder[[idx]], uniqueOrder$mdOrder[[j]])
                        if (same) {
                            uniqueOrder[j, percentage := percentage + candiOrder$percentage[idx]]
                            matchResult = TRUE
                        }
                    }
                    if (!matchResult) {
                        uniqueOrder = rbind(uniqueOrder, candiOrder[idx])
                    }
                }
            }
        }

        validOrder = uniqueOrder[percentage == max(percentage)]$mdOrder[[1]]
        totalPercentage = 0
        for (i in 1:nrow(orderTable)) {
            if (checkSameOrder(orderTable$mdOrder[[i]], validOrder)) {
                totalPercentage = totalPercentage + orderTable$percentage[i]
            }
        }

        ret = rbind(ret, data.table(derivname = der, mdOrder = list(validOrder), percentage = totalPercentage))
    }

    ret[, exchange := FindExchange(marketType, derivName)]
    setcolorder(ret, c("exchange", "derivname"))
    setorder(ret, "exchange", "derivname")
    return(ret)
}

#' nodoc
#' @export
MonitorSessionChg = function(dataSource, monitorDay) {
    marketType = "ChinaFuture"
    cuttingHour = GetTradingDayCuttingHour(marketType)
    cuttingMethod = GetTradingDayCuttingMethod(marketType)
    tradeOnWeekends = GetTradeOnWeekends(marketType)
    tz = GetTimezone(marketType)

    monitorDay = GetTradingDay(MakeTime(monitorDay, tz), dataSource, marketType, TRUE)
    preDay = AddTradingDays(monitorDay, -1, dataSource, marketType)

    normalSession = dataSource$ReadData(marketType, "Misc/Session")
    tradingDay = dataSource$ReadData(marketType, "Misc/TradingDay")
    allSessionData = GetSessionByTradingDay(marketType, normalSession, tradingDay)
    preSession = GetChinaFutureSession(allSessionData, preDay)
    monitorDaySession = GetChinaFutureSession(allSessionData, monitorDay)

    combineData = monitorDaySession[preSession, on = .(derivname)]
    colnames(combineData) = gsub("i\\.", "pre", colnames(combineData))

    monitorData = combineData[tradinghours != pretradinghours]
    if (IsEmpty(monitorData)) return(NULL)

    setorder(monitorData, "derivname")
    return(monitorData)
}