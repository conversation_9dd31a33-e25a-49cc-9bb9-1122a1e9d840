#' nodoc
#' @export
CopyData = function(marketType, dataType, src, dest, symbol = NULL, TransformFunc = NULL, begin = NULL, end = NULL, nThreads = GetPreferredConcurrency("copyData")) {
    InfoLog("CopyData", "Copying {marketType}/{dataType} {src$GetDesc()} => {dest$GetDesc()}")
    InfoLog("CopyData", "Listing data")

    if(is(src, "RQDataSource")) nThreads = 1

    srcInfo = src$ListData(marketType, dataType)
    if (is.null(srcInfo)) {
        InfoLog("CopyData", "Src is empty. Nothing to do")
        return()
    }
    destInfo = dest$ListData(marketType, dataType)

    symbols = if (is.null(symbol)) srcInfo$symbol else symbol

    if (src$SupportMultiSymbolOperation(marketType, dataType)) {
        CopyMultiSymbolData(marketType, dataType, src, dest,
            srcInfo, destInfo, symbols, begin, end, TransformFunc)
    } else {
        CopySingleSymbolData(marketType, dataType, src, dest, srcInfo,
            destInfo, symbols, begin, end, TransformFunc, nThreads)
    }

    InfoLog("CopyData", "Data copied: {marketType}/{dataType}")
}

CopyMultiSymbolData = function(marketType, dataType, src, dest, srcInfo,
    destInfo, symbols, begin, end, TransformFunc) {

    srcBegin = base::min(srcInfo$begin)
    srcEnd = base::max(srcInfo$end)
    if (is.null(destInfo))
        destEnd = NULL
    else
        destEnd = base::max(destInfo$end)

    copyBegin = base::max(srcBegin, destEnd, begin, GetConfig(DefaultBegin))
    copyEnd = base::min(srcEnd, end)

    # misc data may have the same timestamp and sometimes the update is incomplete,
    # so update is allowed if copyBegin equal to copyEnd
    if (copyBegin > copyEnd) {
        InfoLog("CopyMultiSymbolData", "Skipped. Dest data {dest$GetDesc()} is newer than src data {src$GetDesc()}.")
        return(invisible())
    }

    InfoLog("CopyMultiSymbolData", "{marketType}/{dataType} {length(symbols)} symbols {TimeToStr(copyBegin)} => {TimeToStr(copyEnd)}")
    data = src$ReadData(marketType, dataType, NULL, copyBegin, copyEnd)
    if (!is.null(TransformFunc)) data = TransformFunc(data)
    if (IsEmpty(data)) return()

    if (dest$SupportMultiSymbolOperation(marketType, dataType)) {
        dest$WriteData(data)
    } else {
        NotImplemented()
    }
}

CopySingleSymbolData = function(marketType, dataType, src, dest, srcInfo,
    destInfo, symbols, begin, end, TransformFunc, nThreads = 1) {

    CopyFunc = function(i) {
        sym = symbols[i]
        srcBegin = srcInfo[symbol == sym, begin]
        srcEnd = srcInfo[symbol == sym, end]

        if (is.null(destInfo) || is.null(destInfo[symbol == sym]$end)) {
            copyBegin = base::max(srcBegin, begin, GetConfig(DefaultBegin))
            copyEnd = base::min(srcEnd, end)
            if (copyBegin > copyEnd) return()
        } else {
            destEnd = destInfo[symbol == sym, end]
            copyBegin = base::max(srcBegin, destEnd, begin, GetConfig(DefaultBegin))
            copyEnd = base::min(srcEnd, end)
            if (copyBegin >= copyEnd) return()
        }

        InfoLog("CopySingleSymbolData", "#{i}/{nrow(srcInfo)}: {marketType}/{dataType}/{sym} {TimeToStr(copyBegin)} => {TimeToStr(copyEnd)}")

        data = src$ReadData(marketType, dataType, sym, copyBegin, copyEnd)
        if (!is.null(TransformFunc)) data = TransformFunc(data)
        if (IsEmpty(data)) return()
        dest$WriteData(data)
    }

    nThreads = min(nThreads, detectCores(logical = TRUE))
    InfoLog("CopySingleSymbolData", "Copying data with {nThreads} threads")

    mclapply(seq_along(symbols), CopyFunc, mc.cores = nThreads)
}


#' nodoc
#' @export
ConvertDataToBar = function(marketType, dataType, src, dest, barSize = 60, symbol = NULL, TransformFunc = NULL, begin = NULL, end = NULL) {
    InfoLog("ConvertDataToBar", "Compressing {marketType}/{dataType} to Bar/{barSize} {src$GetDesc()} => {dest$GetDesc()}")

    InfoLog("ConvertDataToBar", "Listing bar data")
    srcInfo = src$ListData(marketType, dataType)
    destInfo = dest$ListData(marketType, paste0("Bar/", barSize))

    if (is.null(symbol))
        symbols = srcInfo$symbol
    else
        symbols = symbol

    nThreads = GetPreferredConcurrency()
    InfoLog("ConvertDataToBar", "Converting Data to Bar with {nThreads} threads")

    ret = mclapply(seq_along(symbols), function(i) {
        sym = symbols[i]
        srcBegin = srcInfo[symbol == sym, begin]
        srcEnd = srcInfo[symbol == sym, end]
        if (is.null(destInfo)) {
            destEnd = NULL
        } else {
            destEnd = destInfo[symbol == sym, end]
        }

        b = base::max(srcBegin, destEnd, begin, GetConfig(DefaultBegin))
        e = base::min(srcEnd, end)
        if (b >= e) return(TRUE)

        InfoLog("ConvertDataToBar", "#{i}/{nrow(srcInfo)}: {marketType}/{dataType}/{sym} => {barSize}, {TimeToStr(b)} => {TimeToStr(e)}")

        data = src$ReadData(marketType, dataType, sym, b, e)
        data = FilterData(src, data)
        if (!is.null(TransformFunc)) data = TransformFunc(data)
        if (IsEmpty(data)) return(TRUE)

        e = try_capture_stack({
            if (dataType == "OESIndexData") {
                bar = CompressBar(data, barSize, sym, price = data$last)
            } else {
                bar = CompressBar(data, barSize, sym)
            }
            dest$WriteData(bar)
        }, environment())

        if (OnException(e)) {
            ErrorLog("ConvertDataToBar", "Failed to compress {marketType}/{dataType}/{sym} to Bar/{barSize}: {e$message}")
            return(FALSE)
        }
        return(TRUE)
    }, mc.cores = nThreads)

    gc()
    errorSymbols = symbols[!unlist(ret)]
    if (length(errorSymbols) > 0) {
        ErrorLog("ConvertDataToBar", "Failed to compress {marketType}/{dataType} to Bar/{barSize} for symbols: {errorSymbols}")
    }
    InfoLog("ConvertDataToBar", "Bar/{barSize} compressed for {marketType}")
}



#' nodoc
#' @export
ConvertBarToFutureMajor = function(marketType, src, dest, srcBarSize = 60, by = "vol", derivName = NULL) {
    Assert(by %in% c("vol", "oi"))

    dataType = ifelse(by == "vol", "Misc/Major", "Misc/OIMajor")

    InfoLog("ConvertBarToFutureMajor", "Calculating {marketType}/{dataType} {src$GetDesc()} => {dest$GetDesc()}")

    srcInfo = src$ListData(marketType, paste0("Bar/", srcBarSize))
    if (IsEmpty(srcInfo)) return(NULL)

    srcBegin = min(srcInfo$begin)
    srcEnd = max(srcInfo$end)
    srcInfo[, derivName := GetDerivNameOfSymbol(marketType, symbol)]
    data = split(srcInfo, by = c("derivName"))
    if (!is.null(derivName)) data = data[derivName]

    destInfo = dest$ListData(marketType, dataType)
    existMajor = dest$ReadData(marketType, dataType)

    major = list()
    for (dn in names(data)) {
        InfoLog("ConvertBarToFutureMajor", "Calculating major of derivative name: {dn}")
        symbol = data[[dn]]$symbol

        if (is.null(destInfo) || !dn %in% destInfo[[3]])
            destEnd = NULL
        else
            destEnd = destInfo[destInfo[[3]] == dn]$end

        begin = base::max(srcBegin, destEnd, GetConfig(DefaultBegin))
        end = srcEnd
        if (begin >= end) next

        bar1d = BatchReadBar(src, marketType, symbol, begin, end, fromBarSize = srcBarSize)
        if (IsEmpty(bar1d)) next

        if (dn %in% existMajor$derivname) {
            m = CalcMajor(bar1d, by, dest, latestMajor = last(existMajor[derivname == dn]))
        } else {
            m = CalcMajor(bar1d, by, dest)
        }

        major[[dn]] = m
    }

    allMajor = rbindlist(major)

    if (!IsEmpty(allMajor)) {
        setattr(allMajor, "marketType", marketType)
        setattr(allMajor, "dataType", dataType)
        setorder(allMajor, timestamp, derivname)
        dest$WriteData(allMajor)
    }
}


#' nodoc
#' @export
CopyMiscDataCrossMarketType = function(srcMarketType, destMarketType, dataType, src, dest) {
    if (!startsWith(dataType, "Misc/")) {
        FatalLog("CopyMiscDataCrossMarketType", "Unsupported data type: {dataType}")
    }

    InfoLog("CopyMiscDataCrossMarketType", "Copying {dataType} {src$GetDesc()}/{srcMarketType} => {dest$GetDesc()}/{destMarketType}")

    srcData = src$ReadData(srcMarketType, dataType)
    if (IsEmpty(srcData)) {
        InfoLog("CopyMiscDataCrossMarketType", "Src data is empty. Nothing to do")
        return()
    }

    destData = srcData

    if (dataType == "Misc/Session") {
        if (destMarketType == "ChinaOption") {
            tz = GetTimezone(destMarketType)
            csioSession = GetChinaStockIndexOptionSession(GetToday(tz), dest)
            destData = rbind(destData, csioSession)
            setorder(destData, timestamp, derivname)
        }
    } else if(dataType == "Misc/TradingDay") {
        destData[, timestamp := GetBeginOfTradingDay(tradingday, dest, destMarketType, TRUE)]
    }

    setattr(destData, "marketType", destMarketType)
    setattr(destData, "dataType", dataType)

    destInfo = dest$WriteData(destData)

    InfoLog("CopyMiscDataCrossMarketType", "Data copied: {destMarketType}/{dataType}")
}

#' nodoc
#' @export
RemoveRawDataBefore = function(rawPath,
                                  marketType,
                                  dataType,
                                  time,
                                  archivePath,
                                  archiveCompression = "xz",
                                  compareArchiveData = TRUE,
                                  needConfirm = TRUE) {

    rawDataPath = file.path(rawPath, marketType, dataType)

    time = MakeTime(time)

    rawDataFile = list.files(rawDataPath, full.names = F, recursive = F)

    if (length(rawDataFile) == 0) {
        InfoLog("RemoveRawDataBefore", "Ignore empty path: {rawDataPath}")
        return(TRUE)
    }

    rawDate = file_path_sans_ext(rawDataFile)
    rawDateFormat = GetStrDateFormat(first(rawDate))
    timeStr = DateToStrByFormat(time, rawDateFormat)

    removeIdx = which(rawDate < timeStr)
    removeRawDataFile = rawDataFile[removeIdx]
    removeRawDataFilePath = file.path(rawDataPath, removeRawDataFile)
    removeDate = rawDate[removeIdx]

    if (length(removeRawDataFilePath) == 0) {
        InfoLog("RemoveRawDataBefore", "Skipped: {rawDataPath}. Oldest data timestamp: {first(rawDate)}")
        return(TRUE)
    }

    if (compareArchiveData) {
        InfoLog("RemoveRawDataBefore", "Checking raw data against archive: {rawDataPath}")
        res = TRUE
        for (i in seq_along(removeRawDataFilePath)) {
            checkDate = StrToDateByFormat(removeDate[i])
            equal = CheckRawDataEqualToArchiveData(rawPath, archivePath, marketType, dataType,
                checkDate,
                archiveCompression = archiveCompression
            )
            if (!equal) {
                WarningLog("RemoveRawDataBefore", "Failed: #{i}/{length(removeRawDataFilePath)}, Inconsistency found: {removeRawDataFilePath[i]}")
                res = FALSE
            } else {
                InfoLog("RemoveRawDataBefore", "Passed: #{i}/{length(removeRawDataFilePath)} {removeRawDataFilePath[i]}")
            }
        }
        if (!res) {
            return(FALSE)
        }
    }

    filePathsToRemove = paste0(removeRawDataFilePath, collapse = "\n")
    if (needConfirm) {
        delete = Confirm(glue("Removing files: \n{filePathsToRemove}"))
    } else {
        delete = TRUE
    }

    if (delete) {
        result = unlink(removeRawDataFilePath, recursive = TRUE)
        if (result == 0) {
            InfoLog("RemoveRawDataBefore", "Removed: \n{filePathsToRemove}")
        } else {
            InfoLog("RemoveRawDataBefore", "Failed: unlink() returns {result}")
        }
    }

    return(TRUE)
}

#' nodoc
#' @export
RemoveFSTDataBefore = function(dsRoot,
                               marketType,
                               dataType,
                               time,
                               needConfirm = TRUE) {

    # get file list
    fstDataPath = file.path(dsRoot, marketType, dataType)
    fstDataFile = list.files(fstDataPath, full.names = FALSE, recursive = FALSE)
    if (length(fstDataFile) == 0) {
        InfoLog("RemoveFSTDataBefore", "Ignored empty path: {fstDataPath}")
        return(TRUE)
    }

    # parse file date format
    fstDate = file_path_sans_ext(fstDataFile)
    if (!IsDateFormatString(first(fstDate))) {
        InfoLog("RemoveFSTDataBefore", "Ignored non-date files path: {fstDataPath}")
        return(TRUE)
    }
    time = MakeTime(time)
    fstDateFormat = GetStrDateFormat(first(fstDate))
    timeStr = DateToStrByFormat(time, fstDateFormat)

    # filter files to remove
    removeIdx = which(fstDate < timeStr)
    removeFSTDataFile = fstDataFile[removeIdx]
    removeFSTDataFilePath = file.path(fstDataPath, removeFSTDataFile)
    if (length(removeFSTDataFilePath) == 0) {
        InfoLog("RemoveFSTDataBefore", "Skipped oldest data timestamp: {first(fstDate)} in {fstDataPath}")
        return(TRUE)
    }

    # remove files
    filePathsToRemove = paste0(removeFSTDataFilePath, collapse = "\n")
    if (!needConfirm || Confirm(glue("Removing files: \n{filePathsToRemove}"))) {
        result = unlink(removeFSTDataFilePath, recursive = TRUE)
        if (result == 0) {
            InfoLog("RemoveFSTDataBefore", "Removed: \n{filePathsToRemove}")
        } else {
            InfoLog("RemoveFSTDataBefore", "Failed: unlink() returns {result}")
        }
    }
    return(TRUE)
}

GetRemovePathFromDateSplitFolder = function(dataPath, time = NULL) {
    dataFile = list.files(dataPath, full.names = F, recursive = F)

    if (length(dataFile) == 0) {
        InfoLog("RemoveDataAfter", "Ignore empty path: {dataPath}")
        return()
    }

    rawDate = file_path_sans_ext(dataFile, compression = TRUE)
    checkallDate = all(IsDateFormatString(rawDate))
    if (checkallDate) {
        AssertNotNull(time)
        time = MakeTime(time)
        rawDateFormat = GetStrDateFormat(first(rawDate))
        timeStr = DateToStrByFormat(time, rawDateFormat)

        removeIdx = which(rawDate >= timeStr)
        removeDataFile = dataFile[removeIdx]
        removeDataFilePath = file.path(dataPath, removeDataFile)
    } else {
        removeDataFilePath = dataPath
    }

    return(removeDataFilePath)
}

#' nodoc
GetRemoveFilePath = function(dataRoot, marketType, dataType, time = NULL) {

    dataPath = file.path(dataRoot, marketType, dataType)
    hasDir = dir.exists(dataPath)
    removeDataFilePath = c()

    if (hasDir) {
        removeDataFilePath = GetRemovePathFromDateSplitFolder(dataPath, time)

    } else if (startsWith(dataType, "Misc/")) {
        rawDataPath = file.path(dataRoot, marketType, "Misc")
        miscName = gsub("^Misc/", "", dataType)
        removeDataFilePath = list.files(rawDataPath, pattern = paste0("^", miscName, "\\..*"), full.names = T)

    } else if (startsWith(dataType, "Fundamental/")) {
        dataPath = file.path(dataRoot, marketType, "Fundamental")
        factor = gsub("^Fundamental/", "", dataType)
        removeDataFilePath = list.files(dataPath, pattern = paste0("^", factor, "\\..*"), full.names = T)
        if (length(removeDataFilePath) == 0) {
            removeDataFilePath = GetRemovePathFromDateSplitFolder(dataPath, time)
            removeDataFilePath = file.path(removeDataFilePath, paste0(factor, ".csv"))
        }
    }

    return(removeDataFilePath)
}

#' Remove data files in batch mode for multiple data paths
#' @param dt A data.table containing columns: dataRoot, marketType, dataType
#' @param time The cutoff time, files after this time will be removed
#' @param needConfirm Whether to ask for confirmation before deletion
#' @return TRUE if deletion is successful, FALSE otherwise
#' @export
BatchRemoveDataAfter = function(dt, time = NULL, needConfirm = TRUE) {
    # Validate input data.table format
    requiredCols = c("dataRoot", "marketType", "dataType")
    if (!all(requiredCols %in% names(dt))) {
        stop("Input data.table must contain columns: ", paste(requiredCols, collapse = ", "))
    }

    # Get all file paths to be removed
    setorder(dt, marketType, dataRoot, dataType)
    allFilesToRemove = data.table()
    for (i in seq_len(nrow(dt))) {
        filePaths = GetRemoveFilePath(dt[i]$dataRoot, dt[i]$marketType, dt[i]$dataType, time)
        if (length(filePaths) > 0) {
            allFilesToRemove = rbind(allFilesToRemove, data.table(
                filePath = filePaths,
                marketType = dt[i]$marketType,
                dataType = dt[i]$dataType,
                dsRoot = dt[i]$dataRoot
            ))
        }
    }
    if (nrow(allFilesToRemove) == 0) {
        InfoLog("BatchRemoveDataAfter", "No files to remove")
        return(FALSE)
    }

    # Ask for confirmation before deletion
    filePathsToRemove = paste0(allFilesToRemove$filePath, collapse = "\n")
    InfoLog("BatchRemoveDataAfter", "Files to be removed:\n{filePathsToRemove}")
    delete = !needConfirm || Confirm(glue("Do you want to remove the above files?"))
    if (!delete) {
        InfoLog("BatchRemoveDataAfter", "Remove data canceled")
        return(FALSE)
    }

    # Remove files if confirmed
    removeResult = TRUE
    for (i in seq_len(nrow(allFilesToRemove))) {
        item = allFilesToRemove[i]
        result = unlink(item$filePath, recursive = TRUE)
        if (result != 0) {
            InfoLog("BatchRemoveDataAfter", "Failed to remove: {item$filePath}")
            removeResult = FALSE
            next
        }
        InfoLog("BatchRemoveDataAfter", "Removed: {item$filePath}")
        ClearInfoCache(item$dsRoot, item$marketType, item$dataType)
    }
    return(removeResult)
}

#' Remove data files after specified time
#' @param dataRoot Root directory of data
#' @param marketType Market type
#' @param dataType Data type
#' @param time The cutoff time, files after this time will be removed
#' @param needConfirm Whether to ask for confirmation before deletion
#' @return TRUE if deletion is successful, FALSE otherwise
#' @export
RemoveDataAfter = function(dataRoot, marketType, dataType, time = NULL, needConfirm = TRUE) {
    dt = data.table(
        dataRoot = dataRoot,
        marketType = marketType,
        dataType = dataType
    )
    return(BatchRemoveDataAfter(dt, time, needConfirm))
}

#' nodoc
#' @export
RemoveMiscDataAfter = function(dataSource, marketType, dataType, time, needConfirm = TRUE) {
    dataSourceRoot = dataSource$root
    data = dataSource$ReadData(marketType, dataType)
    beforeData = data[data[[1]] < time]
    if (RemoveDataAfter(dataSourceRoot, marketType, dataType, needConfirm = needConfirm)) {
        dataSource$WriteData(beforeData)
    }
}

#' nodoc
#' @export
SyncRTMD = function(server, remotePath, localPath, marketType, dataType, interval = 60) {

    tz = GetTimezone(marketType)
    cuttingHour = GetTradingDayCuttingHour(marketType)
    cuttingMethod = GetTradingDayCuttingMethod(marketType)
    tradeOnWeekends = GetTradeOnWeekends(marketType)

    RsyncFunc = function(date) {
        date = DateToStr(date)
        srcPath = paste0(server, ":", file.path(remotePath, marketType, dataType, date))
        destPath = file.path(localPath, marketType, dataType)
        cmd = paste("rsync -avz --append --ignore-missing-args", srcPath, destPath)
        InfoLog("SyncRTMD", "Syncing: {srcPath} => {destPath}")
        BashExec(cmd)
    }

    prevTd = GetTradingDayNoDS(GetNow(tz), cuttingHour, cuttingMethod, tradeOnWeekends)
    while (TRUE) {
        td = GetTradingDayNoDS(GetNow(tz), cuttingHour, cuttingMethod, tradeOnWeekends)
        if (prevTd != td) RsyncFunc(prevTd)
        RsyncFunc(td)
        prevTd = td
        Sys.sleep(interval)
    }
}