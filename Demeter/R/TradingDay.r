#' nodoc
#' @export
BOW = MakeTime("1990-01-01 00:00:00")

#' nodoc
#' @export
EOW = MakeTime("2200-01-01 00:00:00")

GetTradingDMYSeqNoDS = function(begin, end, cuttingHour, cuttingMethod, tradeOnWeekends, RcppTradingDMYSeq) {
    AssertNotNull(begin, end, cuttingHour, cuttingMethod, tradeOnWeekends)
    return(RcppTradingDMYSeq(begin, end, cuttingHour, cuttingMethod == 2, tradeOnWeekends))
}

GetTradingDMYSeq = function(begin, end, dataSource, marketType, GetBeginFunc, GetNextBeginFunc) {
    AssertNotNull(begin, end, dataSource, marketType, GetBeginFunc, GetNextBeginFunc)
    tradingDayData = dataSource$ReadData(marketType, "Misc/TradingDay", end = EOW)
    if (end > last(tradingDayData$tradingday) || begin < first(tradingDayData$tradingday)) {
        stop(glue("begin - end: {begin} - {end} out of ds tradingday range: {first(tradingDayData$tradingday)} - {last(tradingDayData$tradingday)}"))
    }
    beginTradingDay = GetTradingDay(begin, dataSource, marketType)
    endTradingDay = GetTradingDay(end, dataSource, marketType)
    validTradingDay = tradingDayData[tradingday >= beginTradingDay & tradingday <= endTradingDay, tradingday]
    ret = NULL
    if (!IsEmpty(validTradingDay)) {
        ret = GetBeginFunc(validTradingDay, dataSource, marketType)
        seqEnd = GetNextBeginFunc(last(validTradingDay), dataSource, marketType)
        ret = unique(c(ret, seqEnd))
    }
    return(ret)
}


#' Get trading day sequence between begin and end, both inclusive,
#' the sequence are the cutting points of the trading day
#' @export
GetTradingDaySeqNoDS = function(begin, end, cuttingHour, tradeOnWeekends) {
    RcppTradingDMYSeq = function(begin, end, cuttingHour, cuttingMethod, tradeOnWeekends) {
        return(RcppTradingDaySeq(begin, end, cuttingHour, tradeOnWeekends))
    }
    return(GetTradingDMYSeqNoDS(begin, end, cuttingHour, 2, tradeOnWeekends, RcppTradingDMYSeq))
}

#' nodoc
#' @export
GetTradingDaySeq = function(begin, end, dataSource, marketType) {
    return(GetTradingDMYSeq(begin, end, dataSource, marketType, GetBeginOfTradingDay, GetBeginOfNextTradingDay))
}

#' Get trading month sequence between begin and end, both inclusive,
#' the sequence are the cutting points of the trading month
#' @export
GetTradingMonthSeqNoDS = function(begin, end, cuttingHour, cuttingMethod, tradeOnWeekends) {
    return(GetTradingDMYSeqNoDS(begin, end, cuttingHour, cuttingMethod, tradeOnWeekends, RcppTradingMonthSeq))
}

#' nodoc
#' @export
GetTradingMonthSeq = function(begin, end, dataSource, marketType) {
    return(GetTradingDMYSeq(begin, end, dataSource, marketType, GetBeginOfTradingMonth, GetBeginOfNextTradingMonth))
}

#' Get trading year sequence between begin and end, both inclusive,
#' the sequence are the cutting points of the trading year
#' @export
GetTradingYearSeqNoDS = function(begin, end, cuttingHour, cuttingMethod, tradeOnWeekends) {
    return(GetTradingDMYSeqNoDS(begin, end, cuttingHour, cuttingMethod, tradeOnWeekends, RcppTradingYearSeq))
}

#' nodoc
#' @export
GetTradingYearSeq = function(begin, end, dataSource, marketType) {
    return(GetTradingDMYSeq(begin, end, dataSource, marketType, GetBeginOfTradingYear, GetBeginOfNextTradingYear))
}


#' nodoc
#' @export
GetTradingDayNoDS = function(time, cuttingHour, cuttingMethod, tradeOnWeekends) {
    Assert(cuttingMethod == 1 || cuttingMethod == 2)
    return(RcppGetTradingDay(time, cuttingHour, cuttingMethod == 2, tradeOnWeekends))
}

#' nodoc
#' @export
GetTradingDay = function(time, dataSource, marketType, noThrow = FALSE) {
    AssertNotNull(time, dataSource, marketType)
    tradingDayData = dataSource$ReadData(marketType, "Misc/TradingDay", end = EOW)
    if (!noThrow && (first(tradingDayData$tradingday) > min(time) || last(tradingDayData$tradingday) < max(time))) {
        stop(glue("Time out of range: {min(time)} - {max(time)}"))
    }
    idx = findInterval(time, tradingDayData$timestamp)
    idx[idx == 0] = 1
    ret = tradingDayData$tradingday[idx]
    ret[time > last(tradingDayData$timestamp)] = MakeTime(DateToStr(EOW))
    ret[time < first(tradingDayData$timestamp)] = MakeTime(DateToStr(BOW))
    return(ret)
}


#' Get trading day sequence
#' @param time Must be aligned to constant interval
#' @export
DeduceTradingDaySeq = function(time) {
    if (length(time) == 0) {
        return(NULL)
    }

    if (length(time) >= 2) {
        timeInterval = diff(time)
        if (GetMode(timeInterval) > days(1)) {
            WarningLog("DeduceTradingDaySeq", "The time interval of input needs to be within one day")
            return(NULL)
        }
    }

    if (length(time) == 1) {
        interval = 1
    } else {
        interval = min(diff(time[1:100]), na.rm = TRUE)
    }

    duration = difftime(last(time), first(time), units = "days")
    durationDays = max(1, ceiling(duration))

    daysSeq = (last(time) + 0.5 * interval) - (durationDays:0) * days(1) # the days cutting points
    if (daysSeq[1] > first(time)) {
        daysSeq = c(daysSeq[1] - days(1), daysSeq)
    }
    # when input time is day bar

    startCuttingPoint = daysSeq[1]
    daysSeq = daysSeq[-1]

    daysSeqIndex = findInterval(daysSeq, time)

    sessionLengthOfEachDay = c(first(daysSeqIndex), diff(daysSeqIndex))
    emptyDayIndex = which(sessionLengthOfEachDay == 0)

    if (length(emptyDayIndex) > 0) {
        daysSeq = daysSeq[-emptyDayIndex] # remove non-trading days
        daysSeqIndex = daysSeqIndex[-emptyDayIndex]
        sessionLengthOfEachDay = sessionLengthOfEachDay[-emptyDayIndex]
    }

    wdaySeq = wday(daysSeq)
    standardSessionLength = GetMode(sessionLengthOfEachDay) # use the mode as length of standard trading days
    isCompelteDay = !wdaySeq %in% c(1, 7) | sessionLengthOfEachDay >= standardSessionLength

    daysSeqIndex = daysSeqIndex[isCompelteDay]
    daysSeq = daysSeq[isCompelteDay]

    for (i in seq_along(daysSeq)) {
        idx = daysSeqIndex[i]
        if (idx == length(time)) next
        nextIdx = idx + 1
        if (time[nextIdx] - time[idx] > interval) next
        nextDayTime = time[idx] + days(1)
        nextDayIdx = findInterval(nextDayTime, time)
        timeDiff = diff(time[idx:nextDayIdx])
        endSessionIdx = c(idx:nextDayIdx)[first(which(timeDiff > interval))]
        if (length(endSessionIdx) == 0) next
        daysSeq[i] = time[endSessionIdx] + 0.5 * interval
    }

    ret = c(startCuttingPoint, daysSeq)

    return(ret)
}




#' Calculate the trading day by adding ndays to the input time.
#' The function will calculate the trading day using standard logic without considering holidays.
#' @param time: the input time. If it falls on a non-trading day, the function will find the nearest previous trading day.
#' @param ndays: the number of days to add, minus means to subtract
#' @param tradeOnWeekends: whether to trade on weekends
#' @export
AddTradingDaysNoDS = function(time, ndays, tradeOnWeekends) {
    AssertNotNull(time, ndays, tradeOnWeekends)
    return(RcppAddTradingDays(time, ndays, tradeOnWeekends))
}

#' Calculate the trading day by adding ndays to the input time.
#' The function will utilize trading day data from dataSource to calculate the trading day, accounting for holidays.
#' @param time: the input time. If it falls on a non-trading day, the function will find the nearest previous trading day.
#' @param ndays: the number of days to add, minus means to subtract
#' @param dataSource: the data source
#' @param marketType: the market type
#' @param noThrow: If TRUE, return `Inf` when out of range. If FALSE, throw an error.
#' @export
AddTradingDays = function(time, ndays, dataSource, marketType, noThrow = FALSE) {
    AssertNotNull(time, ndays, dataSource, marketType)
    tz = attr(time, "tzone")
    cuttingMethod = GetTradingDayCuttingMethod(marketType[[1]])
    cuttingHour = GetTradingDayCuttingHour(marketType[[1]])
    tradingDayData = dataSource$ReadData(marketType, "Misc/TradingDay", end = EOW)
    tradingDay = tradingDayData$tradingday
    timeDay = as.POSIXct(DateToStr(time), tz = tz)
    diff = as.numeric(time - timeDay, units = "secs")
    day = GetTradingDayNoDS(time, cuttingHour, cuttingMethod, TRUE) # tradeOnWeekends = TRUE to get the natural day considering cutting hour and cutting method
    ret = sapply(seq_along(time), function(i) {
        d = day[i]
        if (d < first(tradingDay) || d > last(tradingDay)) {
            if (!noThrow) {
                stop(glue("Time out of range: {time[i]}"))
            }
            return(ifelse(d < first(tradingDay), BOW, EOW))
        }
        idx = which(tradingDay == d)
        if (IsEmpty(idx)) {
            idx = findInterval(d, tradingDay) + 1
        }
        if ((cuttingMethod == 2 && diff[i] >= cuttingHour * 3600) ||
            (!IsTradingDay(time[i], dataSource, marketType, noThrow) && cuttingMethod == 1)) {
            idx = idx - 1
        }
        if (idx + ndays >= length(tradingDay) || idx + ndays <= 0) {
            if (!noThrow) {
                stop(glue("Cannot find trading day for: {time[i]} + {ndays}."))
            }
            return(ifelse(idx + ndays < 0, BOW, EOW))
        }
        return(tradingDay[idx + ndays] + diff[i])
    })
    ret = DoubleToTime(ret, tz)
    return(ret)
}




#' nodoc
#' @export
GetBeginOfTradingDayNoDS = function(time, cuttingHour, tradeOnWeekends) {
    AssertNotNull(time, cuttingHour)
    return(RcppGetBeginOfTradingDay(time, cuttingHour, tradeOnWeekends))
}

#' nodoc
#' @export
GetBeginOfTradingDay = function(time, dataSource, marketType, noThrow = FALSE) {
    AssertNotNull(time, dataSource, marketType)
    tradeOnWeekends = GetTradeOnWeekends(marketType[[1]])
    cuttingHour = GetTradingDayCuttingHour(marketType[[1]])
    cuttingMethod = GetTradingDayCuttingMethod(marketType[[1]])
    if (cuttingMethod == 2) {
        time = AddTradingDays(time, -1, dataSource, marketType, noThrow)
        ret = GetBeginOfNextTradingDayNoDS(time, cuttingHour, tradeOnWeekends)
    } else {
        time = GetTradingDay(time, dataSource, marketType, noThrow)
        ret = GetBeginOfTradingDayNoDS(time, cuttingHour, tradeOnWeekends)
    }
    return(ret)
}



#' nodoc
#' @export
GetBeginOfNextTradingDayNoDS = function(time, cuttingHour, tradeOnWeekends) {
    AssertNotNull(time, cuttingHour)
    return(RcppGetBeginOfNextTradingDay(time, cuttingHour, tradeOnWeekends))
}

#' nodoc
#' @export
GetBeginOfNextTradingDay = function(time, dataSource, marketType, noThrow = FALSE) {
    AssertNotNull(time, dataSource, marketType)
    cuttingHour = GetTradingDayCuttingHour(marketType[[1]])
    tradeOnWeekends = GetTradeOnWeekends(marketType[[1]])
    cuttingMethod = GetTradingDayCuttingMethod(marketType[[1]])
    tradingDay = GetTradingDay(time, dataSource, marketType, noThrow)
    if (cuttingMethod == 2) {
        ret = GetBeginOfNextTradingDayNoDS(tradingDay, cuttingHour, tradeOnWeekends)
    } else {
        ret = AddTradingDays(tradingDay, 1, dataSource, marketType, noThrow)
        ret = GetBeginOfTradingDayNoDS(ret, cuttingHour, tradeOnWeekends)
    }
    return(ret)
}


#' nodoc
#' @export
GetBeginOfTradingMonthNoDS = function(time, cuttingHour, cuttingMethod, tradeOnWeekends) {
    return(RcppGetBeginOfTradingMonth(time, cuttingHour, cuttingMethod == 2, tradeOnWeekends))
}

#' nodoc
#' @export
GetBeginOfTradingMonth = function(time, dataSource, marketType, noThrow = FALSE) {
    prevMonthLastDay = floor_date(time, "month") - days(1)
    cuttingMethod = GetTradingDayCuttingMethod(marketType)
    if (cuttingMethod == 2) {
        nonTradingDayIdx = which(!IsTradingDay(prevMonthLastDay, dataSource, marketType, noThrow))
        if (!IsEmpty(nonTradingDayIdx))
            prevMonthLastDay[nonTradingDayIdx] = AddTradingDays(prevMonthLastDay[nonTradingDayIdx], -1, dataSource, marketType, noThrow)
    }
    ret = GetBeginOfNextTradingDay(prevMonthLastDay, dataSource, marketType, noThrow)
    return(ret)
}


#' nodoc
#' @export
GetBeginOfNextTradingMonthNoDS = function(time, cuttingHour, cuttingMethod, tradeOnWeekends) {
    return(RcppGetBeginOfNextTradingMonth(time, cuttingHour, cuttingMethod == 2, tradeOnWeekends))
}

#' nodoc
#' @export
GetBeginOfNextTradingMonth = function(time, dataSource, marketType, noThrow = FALSE) {
    nextMonth = ceiling_date(time, "month") + days(10)
    ret = GetBeginOfTradingMonth(nextMonth, dataSource, marketType, noThrow)
    return(ret)
}


#' nodoc
#' @export
GetBeginOfTradingYearNoDS = function(time, cuttingHour, cuttingMethod, tradeOnWeekends) {
    return(RcppGetBeginOfTradingYear(time, cuttingHour, cuttingMethod == 2, tradeOnWeekends))
}

#' nodoc
#' @export
GetBeginOfTradingYear = function(time, dataSource, marketType, noThrow = FALSE) {
    firstDay = floor_date(time, "year")
    tradingDayData = dataSource$ReadData(marketType, "Misc/TradingDay", end = EOW)
    if (!noThrow && (first(tradingDayData$tradingday) > min(time) || last(tradingDayData$tradingday) < max(time))) {
        stop(glue("Time out of tradingday range: {min(time)} or {max(time)}"))
    }
    idx = findInterval(firstDay, tradingDayData$tradingday)
    cuttingMethod = GetTradingDayCuttingMethod(marketType)
    if (cuttingMethod == 2) {
        idx[idx == 0] = 1
        ret = tradingDayData$timestamp[idx]
    } else {
        ret = tradingDayData$timestamp[idx + 1]
    }
    earlierIdx = which(time < first(tradingDayData$tradingday))
    laterIdx = which(time > last(tradingDayData$tradingday))
    ret[earlierIdx] = BOW
    ret[laterIdx] = EOW
    return(ret)
}


#' nodoc
#' @export
GetBeginOfNextTradingYearNoDS = function(time, cuttingHour, cuttingMethod, tradeOnWeekends) {
    return(RcppGetBeginOfNextTradingYear(time, cuttingHour, cuttingMethod == 2, tradeOnWeekends))
}

#' nodoc
#' @export
GetBeginOfNextTradingYear = function(time, dataSource, marketType, noThrow = FALSE) {
    nextYear = ceiling_date(time, "year") + days(10)
    ret = GetBeginOfTradingYear(nextYear, dataSource, marketType, noThrow)
    return(ret)
}


#' nodoc
#' @export
GetTradingDaysBetween = function(marketType, dataSource, begin, end, noThrow = FALSE) {
    marketType = marketType[[1]]
    tradeOnWeekends = GetTradeOnWeekends(marketType)
    cuttingHour = GetTradingDayCuttingHour(marketType)
    begin = GetBeginOfTradingDayNoDS(begin, cuttingHour, tradeOnWeekends)
    end = GetBeginOfTradingDayNoDS(end, cuttingHour, tradeOnWeekends)
    tradingDays = dataSource$ReadData(marketType, "Misc/TradingDay", end = EOW)
    if (last(tradingDays$tradingday) < end) {
        if (noThrow) {
            return(Inf)
        }
        stop(glue("End date out of range: {end}"))
    }
    beginIdx = which(tradingDays$tradingday == as.Date(begin))
    endIdx = which(tradingDays$tradingday == as.Date(end))
    if (length(beginIdx) == 0) {
        beginIdx = findInterval(tradingDays$tradingday,as.Date(begin))
    }
    if (length(endIdx) == 0) {
        endIdx = findInterval(tradingDays$tradingday, as.Date(end))
    }
    return(endIdx - beginIdx)
}


#' nodoc
#' @export
GetTradingDaysBetweenNoDS = function(marketType, begin, end) {
    marketType = marketType[[1]]
    tradeOnWeekends = GetTradeOnWeekends(marketType)
    cuttingHour = GetTradingDayCuttingHour(marketType)
    begin = GetBeginOfTradingDayNoDS(begin, cuttingHour, tradeOnWeekends)
    end = GetBeginOfTradingDayNoDS(end, cuttingHour, tradeOnWeekends)
    tradingDays = GetTradingDaySeqNoDS(min(begin, end), max(begin, end), cuttingHour, tradeOnWeekends)
    return(ifelse(begin > end, -length(tradingDays), length(tradingDays)))
}


#' judge if the time is a trading day by checking the trading day data
#' stop if the time is out of range unless noThrow is TRUE, then return IsTradingDayNoDS
#' @export
IsTradingDay = function(time, dataSource, marketType, noThrow = FALSE) {
    tradingDays = dataSource$ReadData(marketType, "Misc/TradingDay", end = EOW)
    # tradeOnWeekends = TRUE to get the natural day considering cutting hour and cutting method
    date = GetTradingDayNoDS(time, GetTradingDayCuttingHour(marketType), GetTradingDayCuttingMethod(marketType), TRUE)
    ret = rep(TRUE, length(time))
    normalIdx = seq_along(time)
    if (max(date) > last(tradingDays$tradingday) || min(date) < first(tradingDays$tradingday)) {
        if (!noThrow) {
            stop(glue("Out of tradingday range: {max(date)} or {min(date)}"))
        }
        outOfRangeIdx = which(date > last(tradingDays$tradingday) | date < first(tradingDays$tradingday))
        normalIdx = setdiff(normalIdx, outOfRangeIdx)
        ret[outOfRangeIdx] = IsTradingDayNoDS(time[outOfRangeIdx], marketType)
    }
    ret[normalIdx] = date[normalIdx] %in% tradingDays$tradingday
    return(ret)
}

#' nodoc
#' @export
IsTradingDayNoDS = function(time, marketType) {
    tradeOnWeekends = GetTradeOnWeekends(marketType)
    # tradeOnWeekends = TRUE to get the natural day considering cutting hour and cutting method
    date = GetTradingDayNoDS(time, GetTradingDayCuttingHour(marketType), GetTradingDayCuttingMethod(marketType), TRUE)
    if (!tradeOnWeekends) {
        return(!weekdays(date) %in% c("Saturday", "Sunday"))
    }
    NotImplemented()
}