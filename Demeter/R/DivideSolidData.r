WriteDataToFile = function(data, path, fileName) {
    if (IsEmpty(data)) {
        return()
    }
    if (!dir.exists(path)) dir.create(path, recursive = TRUE)
    file = file.path(path, paste0(fileName, ".csv"))
    if (!file.exists(file)) {
        fwrite(data, file)
    } else {
        colClasses = ifelse("symbol" %in% colnames(fread(file, nrows = 1)), c(symbol = "character"), NULL)
        existing = fread(file, header = TRUE, colClasses = colClasses, tz = "")
        for (col in colnames(existing)) {
            if (class(existing[[col]]) != class(data[[col]])) {
                convertFunc = get(paste0("as.", class(data[[col]])))
                existing[[col]] <- convertFunc(existing[[col]])
            }
        }
        data = rbind(existing, data)
        data = unique(data, by = colnames(data))
        fwrite(data, file)
    }
}

GetSolidDivideDate = function(solidDataPath, divideDate, begin) {
    if (!is.null(divideDate)) {
        dates = divideDate
    } else {
        dates = unique(unlist(sapply(solidDataPath, function(path) {
            files = list.files(path, full.names = FALSE, recursive = FALSE)
            regmatches(files, regexpr("^[0-9]{4}-[0-9]{1,2}-[0-9]{1,2}", files))
        })))
    }

    if (!is.null(begin)) {
        begin = MakeTime(begin)
        dates = dates[which(StrToDate(dates) >= begin)]
    }

    return(dates)
}

RemoveDuplicatedSolidData = function(data, badDataRoot, marketType, dataType, date) {
    dupIndex = which(duplicated(data))

    if (length(dupIndex) > 0) {
        logData = c()
        lowerIdx = ifelse(dupIndex - 2 >= 1, dupIndex - 2, 1)
        upperIdx = ifelse(dupIndex + 2 <= nrow(data), dupIndex + 2, nrow(data))

        recordIndex = c()
        for (i in seq_along(dupIndex)) {
            recordIndex = c(recordIndex, lowerIdx[i]:upperIdx[i])
        }
        recordIndex = sort(unique(recordIndex))
        logData = cbind(remove = recordIndex %in% dupIndex, index = recordIndex, data[recordIndex])
        data = unique(data)

        # write duplicate data to log
        dupPath = file.path(badDataRoot, date)
        filePrefix = paste0(marketType, "_", dataType)
        WriteDataToFile(logData, dupPath, paste0(filePrefix, "_DuplicatedData"))

        WarningLog("RemoveDuplicatedSolidData", "Duplicated data removed and recorded in {dupPath}, count: {length(dupIndex)}")
    }

    return(data)
}

CheckDataTimestampOrder = function(data, stopOnDataError = TRUE) {
    if (is.unsorted(data$timestamp, strictly = TRUE)) {
        timeRank = rank(data$timestamp)
        invalidIdx = which(diff(timeRank) < 0) + 1
        if (stopOnDataError) {
            len = length(invalidIdx)
            if (len > 10) {
                invalidIdx = invalidIdx[1:10]
            }
            ErrorLog("CheckDataTimestampOrder", "Unsorted timestamp count: {len}, head indexes are: {toString(head(invalidIdx))}.")
            FatalLog("CheckDataTimestampOrder", "{DataTableToStr(data[invalidIdx,])}")
        } else {
            len = length(invalidIdx)
            ErrorLog("CheckDataTimestampOrder", "Unsorted timestamp count: {len}, head indexes are: {toString(head(invalidIdx))}.")
            ErrorLog("CheckDataTimestampOrder", "{DataTableToStr(data[invalidIdx,])}")
            ErrorLog("CheckDataTimestampOrder", "Start to remove invalid data.")
            for (startIdx in rev(invalidIdx)) {
                lastRank = timeRank[startIdx - 1]
                endIdx = startIdx
                while (endIdx < length(timeRank) && timeRank[endIdx] < lastRank) {
                    endIdx = endIdx + 1
                }
                data = data[-(startIdx:(endIdx - 1)), ]
            }
        }
    }
    return(data)
}

DivideDataAndWriteToFile = function(solidData, destDataRoot, marketType, dataType, date, checkTimestampOrder, stopOnDataError) {
    allSymbolData = split(solidData, by = c("symbol"))
    nThreads = GetPreferredConcurrency("divideData")
    InfoLog("DivideDataAndWriteToFile", "Dividing data with {nThreads} threads.")
    fileMgr = GetDefaultFileManager(destDataRoot)
    dataPath = fileMgr$GetDirPath(marketType, dataType, date)

    mclapply(seq_along(allSymbolData), \(i) {
        sym = names(allSymbolData)[i]
        InfoLog("DivideDataAndWriteToFile", "#{i}/{length(allSymbolData)} Divide data: {marketType}/{dataType} {date} {sym}")
        data = allSymbolData[[i]]

        if (checkTimestampOrder) {
            data = CheckDataTimestampOrder(data, stopOnDataError)
        }

        data[, symbol := NULL]

        # reorder column to make timestamp and exchtime to be the first 2 columns
        if ("exchtime" %in% colnames(data)) setcolorder(data, "exchtime")
        if ("timestamp" %in% colnames(data)) setcolorder(data, "timestamp")

        WriteDataToFile(data, dataPath, sym)
    }, mc.cores = nThreads)

    rm(allSymbolData)
}

#' nodoc
#' @export
GetSolidDataByPath = function(path) {
    InfoLog("GetSolidDataByPath", "Reading solid data: {path}")
    if (!file.exists(path)) {
        WarningLog("GetSolidDataByPath", "Path does not exist: {path}")
        return(NULL)
    }
    withCallingHandlers(
        {
            data = fread(path, header = TRUE, colClasses = c(symbol = "character"), tz = "")
        },
        warning = function(w) {
            WarningLog("GetSolidDataByPath", "Failed to read data from {path}: {w$message}")
            invokeRestart("muffleWarning")
        }
    )
    return(data)
}

#' nodoc
#' @export
DivideSolidData = function(srcDataRoot, destDataRoot, badDataRoot, marketType, dataType, fullScan = FALSE, lastestUpdate = T, divideDate = NULL, begin = NULL, checkTimestampOrder = TRUE, stopOnDataError = TRUE) {
    srcFileMgr = GetFileManagerEntry()$solid
    srcPath = sapply(srcDataRoot, \(root) srcFileMgr$GetDirPath(marketType, dataType, NULL, basename(root)))
    srcDates = GetSolidDivideDate(srcPath, divideDate, begin)
    destFileMgr = GetDefaultFileManager(destDataRoot)
    destDates = destFileMgr$ListFiles(marketType, dataType)

    for (date in srcDates) {
        # skip if date is not latest date
        if (is.null(divideDate) && fullScan == FALSE && length(destDates) > 0 &&
            ((lastestUpdate && date < last(destDates)) || (date %in% destDates && date != last(destDates)))) {
            next
        }

        for (srcRoot in srcDataRoot) {
            # remove empty files
            srcFileMgr = GetDefaultFileManager(srcRoot)
            filePath = srcFileMgr$GetFilePath(marketType, dataType, date, NULL, basename(srcRoot))
            data = GetSolidDataByPath(filePath)
            if (IsEmpty(data)) {
                InfoLog("DivideSolidData", "Remove empty solid data file: {filePath}")
                file.remove(filePath)
                next
            }
            # check if data already divided
            destFiles = destFileMgr$ListFiles(marketType, dataType, date)
            destSymbol = file_path_sans_ext(destFiles)
            if (all(unique(data$symbol) %in% destSymbol)) {
                rm(data)
                gc()
                next
            }
            # check ts order
            if (checkTimestampOrder) data = CheckDataTimestampOrder(data, stopOnDataError)
            data = RemoveDuplicatedSolidData(data, badDataRoot, marketType, dataType, date)

            # split data
            checkSplitedDataTimestampOrder = !checkTimestampOrder
            DivideDataAndWriteToFile(data, destDataRoot, marketType, dataType, date, checkSplitedDataTimestampOrder, stopOnDataError)

            # release data memorey
            rm(data)
            gc()
        }
    }

    InfoLog("DivideSolidData", "Divide solid data done")
}

#' nodoc
#' @export
GetMiscDataFromSolidFile = function(solidFileRoot, marketType, dataType, destSource) {
    if (!startsWith(dataType, "Misc/")) {
        FatalLog("GetMiscDataFromSolidFile", "Unsupport data type: {dataType}")
    }

    InfoLog("GetMiscDataFromSolidFile", "Updating {marketType}/{dataType} {solidFileRoot} => {destSource$GetDesc()}")

    listInfo = destSource$ListData(marketType, dataType)
    if (is.null(listInfo)) {
        begin = GetConfig(DefaultBegin)
    } else {
        begin = last(listInfo[[1]])
    }

    # list solid dates
    pathFilter = basename(solidFileRoot)
    fileMgr = GetDefaultFileManager(solidFileRoot)
    files = fileMgr$ListFiles(marketType, dataType, pathFilter)
    dates = file_path_sans_ext(files)
    dates = unique(dates)

    # filter by time and write misc data to dest
    for (date in dates) {
        dateTime = MakeTime(date, GetTimezone(marketType))
        dateTime = GetTradingDay(dateTime, destSource, marketType, TRUE)
        dateBegin = GetBeginOfTradingDay(dateTime, destSource, marketType, TRUE)
        if (dateBegin <= begin) next

        path = fileMgr$GetFilePath(marketType, dataType, date, NULL, pathFilter)
        data = GetSolidDataByPath(path)
        if (IsEmpty(data)) {
            WarningLog("GetMiscDataFromSolidFile", "Solid data is empty on {date}")
            next
        }

        InfoLog("GetMiscDataFromSolidFile", "Updating {marketType}/{dataType} {date}")
        data = cbind(timestamp = dateBegin, data)
        setorderv(data, cols = colnames(data)[1:2])
        setattr(data, "marketType", marketType)
        setattr(data, "dataType", dataType)
        destSource$WriteData(data)
    }

    InfoLog("GetMiscDataFromSolidFile", "Data updated: {marketType}/{dataType}")
}

#' nodoc
#' @export
CheckSolidDataSizeAgainstYesterday = function(rawDataRoot, marketType, dataType, dataSource,
                                                sizeDiffTolerance = 1 / 4, checkTime = GetToday(GetTimezone(marketType))) {
    targetTradingDay = GetTradingDay(checkTime, dataSource, marketType, TRUE)

    solidFileMgr = GetFileManagerEntry()$solid
    solidDataPath = solidFileMgr$GetDirPath(marketType, dataType, pathFilter = basename(rawDataRoot))
    solidFiles = solidFileMgr$ListFiles(marketType, dataType, pathFilter = basename(rawDataRoot))

    if (length(solidFiles) == 0) {
        WarningLog("CheckSolidDataSizeAgainstYesterday", "No files in solid data path: {solidDataPath}")
        return(FALSE)
    }

    targetSolidDataFile = solidFileMgr$GetFilePath(marketType, dataType, DateToStr(targetTradingDay), pathFilter = basename(rawDataRoot))

    if (last(solidFiles) != basename(targetSolidDataFile)) {
        WarningLog("CheckSolidDataSizeAgainstYesterday", "Latest file does not equal to {targetSolidDataFile}")
        return(FALSE)
    }

    if (length(solidFiles) == 1) {
        return(TRUE)
    }

    last2DaysHaveHolidy = AddTradingDaysNoDS(targetTradingDay, -2, GetTradeOnWeekends(marketType)) !=
        AddTradingDays(targetTradingDay, -2, dataSource, marketType)

    if (last2DaysHaveHolidy) { # crosses public holiday. double the tolerance
        sizeDiffTolerance = sizeDiffTolerance * 2
    }

    targetSolidDataSize = file.size(targetSolidDataFile)
    preSolidDataFile = file.path(solidDataPath, solidFiles[length(solidFiles) - 1])
    preSolidDataSize = file.size(preSolidDataFile)

    fileSizeRatio = targetSolidDataSize / preSolidDataSize
    minRatio = 1 - sizeDiffTolerance
    maxRatio = 1 / (1 - sizeDiffTolerance)
    ret = minRatio <= fileSizeRatio && fileSizeRatio <= maxRatio

    if (!ret) {
        InfoLog(
            "CheckSolidDataSizeAgainstYesterday", "Target file : ",
            targetSolidDataFile, " size is ", targetSolidDataSize, "\n",
            "Pre file : ", preSolidDataFile, " size is ", preSolidDataSize, "\n",
            "File size ratio : ", fileSizeRatio, " is beyond tolerance ", sizeDiffTolerance
        )
    }

    return(ret)
}
