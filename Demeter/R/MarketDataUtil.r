#' nodoc
#' @export
DivideDataByTradingDay = function(data, cuttingHour, cuttingMethod, tradeOnWeekends, by = "closeTime") {
    if (IsEmpty(data)) return(NULL)

    Assert(is.data.table(data))
    data[, tempTrandingDay := GetTradingDayNoDS(get(by), cuttingHour, cuttingMethod, tradeOnWeekends)]
    ret = split(data, by = "tempTrandingDay", keepBy = FALSE)
    for (v in ret) {
        CopyAttr(data, v, "class")
    }
    data[, tempTrandingDay := NULL]
    return(ret)
}

#' nodoc
#' @export
TotalVolToLastVol = function(data, dataSource) {
    mkt = GetMarketType(data)
    if (is.null(mkt) || !"totalvol" %in% names(data)) return(NULL)
    tdSeq = GetTradingDaySeq(first(data$exchtime), last(data$exchtime), dataSource, mkt)
    tdBegin = tdSeq[-length(tdSeq)]
    tdEnd = tdSeq[-1]

    ret = list()
    for (i in seq_along(tdBegin)) {
        b = tdBegin[i]
        e = tdEnd[i]
        tdData = data[exchtime >= b & exchtime <= e]
        if (IsEmpty(tdData)) next
        lastVol = c(first(data$totalvol), diff(data$totalvol))
        ret[[length(ret) + 1]] = lastVol
    }
    return(do.call("c", ret))
}

#' nodoc
#' @export
TotalValueToLastValue = function(data, dataSource) {
    mkt = GetMarketType(data)
    if (is.null(mkt) || !"totalvalue" %in% names(data)) return(NULL)

    tdSeq = GetTradingDaySeq(first(data$exchtime), last(data$exchtime), dataSource, mkt)
    tdBegin = tdSeq[-length(tdSeq)]
    tdEnd = tdSeq[-1]

    ret = list()
    for (i in seq_along(tdBegin)) {
        b = tdBegin[i]
        e = tdEnd[i]
        tdData = data[exchtime >= b & exchtime <= e]
        if (IsEmpty(tdData)) next
        lastValue = c(first(data$totalvalue), diff(data$totalvalue))
        ret[[length(ret) + 1]] = lastValue
    }
    return(do.call("c", ret))
}

#' nodoc
#' @export
GetSumByDay = function(TimeSeries,
                      timeCol = "timestamp",
                      sumCol = "margin",
                      normalize = TRUE) {

    tdSeq = DeduceTradingDaySeq(TimeSeries[[timeCol]])

    allData = rbindlist(lapply(seq_along(tdSeq[-1]), function(i) {
        dayData = TimeSeries[get(timeCol) >= tdSeq[i] &
                               get(timeCol) < tdSeq[i + 1]]
        ret = data.table(timestamp = dayData[[timeCol]], value = dayData[[sumCol]])
    }))

    allData[, itime := timestamp - trunc(timestamp, "days")]
    firstTd = trunc(first(allData$timestamp), "days")

    sumData = allData[, lapply(.SD, sum), .SDcols = "value", by = "itime"]
    if (normalize) sumData[, value := value / max(value)]
    sumData[, itime := as.numeric(itime)]

    if (is.unsorted(sumData$itime)) {
        idx = which(diff(sumData$itime) < 0)
        Assert(length(idx) == 1)
        itime1 = sumData[1:idx, itime + firstTd]
        itime2 = sumData[(idx + 1):nrow(sumData), itime + firstTd + days(1)]
        sumData[, itime := c(itime1, itime2)]
    } else {
        sumData[, itime := itime + firstTd]
    }

    names(sumData) = c(timeCol, sumCol)
    return(sumData)
}