#' nodoc
#' @export
GetTradingDaysToMaturity = function(marketType, symbol, dataSource, time = GetNow(), noThrow = FALSE) {
    maturityTime = GetMaturityTime(marketType, symbol, dataSource, noThrow)$maturityTime
    tradingDays = sapply(maturityTime, function(t) {
        d = GetTradingDaysBetween(marketType, dataSource, time, t, TRUE)
        return(d)
    })
    ret = data.table(marketType, symbol, tradingDaysToMaturity = tradingDays)
    return(ret)
}


#' nodoc
#' @export
GetTradingDaysToMaturityNoDS = function(marketType, symbol, time = GetNow()) {
    maturityTime = GetMaturityTimeNoDS(marketType, symbol)$maturityTime
    tradingDays = sapply(maturityTime, function(t) {
        d = GetTradingDaysBetweenNoDS(marketType, time, t)
        return(d)
    })
    ret = data.table(marketType, symbol, tradingDaysToMaturity = tradingDays)
    return(ret)
}


#' nodoc
#' @export
GetDaysToMaturity = function(marketType, symbol, dataSource, time = GetNow(), noThrow = FALSE) {
    marketType = marketType[[1]]
    tz = GetTimezone(marketType)
    maturityTime = GetMaturityTime(marketType, symbol, dataSource, noThrow)$maturityTime
    days = as.numeric(as.Date(maturityTime, tz) - as.Date(time, tz))
    ret = data.table(marketType, symbol, daysToMaturity = days)
    return(ret)
}

#' nodoc
#' @export
GetDaysToMaturityNoDS = function(marketType, symbol, time = GetNow()) {
    marketType = marketType[[1]]
    tz = GetTimezone(marketType)
    maturityTime = GetMaturityTimeNoDS(marketType, symbol)$maturityTime
    days = as.numeric(as.Date(maturityTime, tz) - as.Date(time, tz))
    ret = data.table(marketType, symbol, daysToMaturity = days)
    return(ret)
}


#' nodoc
#' @export
GetMaturityTime = function(marketType, symbol, dataSource, noThrow = FALSE) {
    marketType = marketType[[1]]
    tradeOnWeekends = GetTradeOnWeekends(marketType)
    cuttingHour = GetTradingDayCuttingHour(marketType)

    tz = GetTimezone(marketType)

    futureYMD = MakeTime(sapply(symbol, function(s) {
        MakeTimeFast(GetFutureYearMonth(s) * 100 + 1)
    }))

    maturityTime = GetBeginOfTradingDay(futureYMD, dataSource, marketType, noThrow) - seconds(1)
    ret = data.table(marketType, symbol, maturityTime = MakeTime(maturityTime, tz))

    CFFEXIdx = which(IsCFFEXFuture(marketType, symbol))
    if (length(CFFEXIdx) > 0) {
        wday(futureYMD)[wday(futureYMD) < 7] = 6
        wday(futureYMD)[wday(futureYMD) == 7] = 13
        futureYMD = futureYMD + weeks(2)
        nonTradingDayIdx = which(!IsTradingDay(futureYMD, dataSource, marketType, noThrow))
        if (length(nonTradingDayIdx) > 0) {
            futureYMD[nonTradingDayIdx] = AddTradingDays(futureYMD[nonTradingDayIdx], 1, dataSource, marketType, TRUE)
        }
        maturityTime = GetBeginOfNextTradingDay(futureYMD, dataSource, marketType, noThrow) - seconds(1)
        value = MakeTime(maturityTime, tz)
        ret[CFFEXIdx, "maturityTime"] = value[CFFEXIdx]
    }

    return(ret)
}


#' nodoc
#' @export
GetMaturityTimeNoDS = function(marketType, symbol) {
    marketType = marketType[[1]]
    tradeOnWeekends = GetTradeOnWeekends(marketType)
    cuttingHour = GetTradingDayCuttingHour(marketType)

    tz = GetTimezone(marketType)

    futureYMD = MakeTime(sapply(symbol, function(s) {
        MakeTimeFast(GetFutureYearMonth(s) * 100 + 1)
    }))
    maturityTime = GetBeginOfTradingDayNoDS(futureYMD, cuttingHour, tradeOnWeekends) - seconds(1)
    ret = data.table(marketType, symbol, maturityTime = MakeTime(maturityTime, tz))

    CFFEXIdx = which(IsCFFEXFuture(marketType, symbol))
    if (length(CFFEXIdx) > 0) {
        wday(futureYMD)[wday(futureYMD) < 7] = 6
        wday(futureYMD)[wday(futureYMD) == 7] = 13
        futureYMD = futureYMD + weeks(2)
        nonTradingDayIdx = which(!IsTradingDayNoDS(futureYMD, marketType[[1]]))
        if (length(nonTradingDayIdx) > 0) {
            futureYMD[nonTradingDayIdx] = AddTradingDaysNoDS(futureYMD[nonTradingDayIdx], 1, tradeOnWeekends)
        }
        maturityTime = GetBeginOfNextTradingDayNoDS(futureYMD, cuttingHour, tradeOnWeekends) - seconds(1)
        value = MakeTime(maturityTime, tz)
        ret[CFFEXIdx, "maturityTime"] = value[CFFEXIdx]
    }
    return(ret)
}