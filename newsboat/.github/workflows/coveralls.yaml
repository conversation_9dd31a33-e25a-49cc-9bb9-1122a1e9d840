name: Coveralls

on: [push, pull_request]

jobs:
  gen_coverage:
    name: Calculate test coverage
    runs-on: ubuntu-24.04

    env:
      # We use Rust 1.86, which builds upon LLVM 19. We have to use a matching
      # C++ compiler, otherwise <PERSON><PERSON><PERSON> won't be able to produce the coverage
      # report.
      CC: clang-19
      CXX: clang++-19
      # Enable test coverage.
      PROFILE: 1
      CXXFLAGS: '-O0 -fprofile-instr-generate -fcoverage-mapping'
      RUSTFLAGS: '-Clink-dead-code -Cinstrument-coverage'
      # Some of our tests use ncurses, which require a terminal of some sort.
      # We pretend ours is a simple one.
      TERM: 'dumb'
      # This prevents our tests from hogging too much of the CPU and failing
      # due to races.
      RUST_TEST_THREADS: 2
      LLVM_PROFILE_FILE: '/tmp/newsboat-profiles/%h_%m.profraw'

    steps:
      - name: Install dependencies
        run: |
          sudo apt-get update
          sudo apt-get install --assume-yes --no-install-suggests clang-19 libsqlite3-dev libcurl4-openssl-dev libxml2-dev libstfl-dev libjson-c-dev libncursesw5-dev

      - name: Generate locales
        run: |
          sudo apt-get install --assume-yes locales
          echo 'en_US.UTF-8 UTF-8' | sudo tee --append /etc/locale.gen
          echo 'ru_RU.KOI8-R KOI8-R' | sudo tee --append /etc/locale.gen
          echo 'ru_RU.CP1251 CP1251' | sudo tee --append /etc/locale.gen
          sudo locale-gen

      - name: Install Rust
        # The last version based on LLVM 19. We have to match LLVM versions
        # used to compile Rust and C++, otherwise they might generate
        # incompatible coverage reports
        uses: dtolnay/rust-toolchain@1.86
        with:
          components: llvm-tools-preview

      - uses: actions/checkout@v4

      - name: Cache ~/.cargo
        uses: actions/cache@v4
        id: cargo_cache
        with:
          key: cargo2-${{ hashFiles('Cargo.lock', '**/Cargo.toml') }}
          path: |
            ~/.cargo/bin
            ~/.cargo/git
            ~/.cargo/registry

      - name: Install grcov
        if: steps.cargo_cache.outputs.cache-hit != 'true'
        run: cargo install grcov

      - name: Remove .gitignore
        # Workaround for https://github.com/mozilla/grcov/issues/1333
        run: rm .gitignore

      - name: Run tests
        run: make --jobs=3 NEWSBOAT_RUN_IGNORED_TESTS=1 ci-check

      - name: Calculate test coverage
        run: grcov /tmp/newsboat-profiles/ --source-dir . --binary-path . --ignore-not-existing --ignore='/*' --ignore='3rd-party/*' --ignore='doc/*' --ignore='test/*' --ignore='target/*' --ignore='newsboat.cpp' --ignore='podboat.cpp' -t lcov -o coverage.lcov

      - name: Submit coverage to Coveralls
        uses: coverallsapp/github-action@v2
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          path-to-lcov: coverage.lcov
