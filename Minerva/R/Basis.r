#' calculate basis between product1 and product1
#' marketType
#' @param dataSource1  dataSource for product1
#' @param prodcut1  A productList class, but only have one row
#' @param dataSource2  dataSource for productList2
#' @param prodcut2  A productList class, but only have one row
# return: data.table(timestamp, diff, basis) :  diff is calcuate as price1 - price2
#                                               basis is calcuate as diff / price2
#' @export
GetBasis = function(dataSource1, product1, dataSource2, productList2, begin = GetConfig(DefaultBegin), end = GetConfig(DefaultEnd)) {

    mkt1 = product1$marketType
    dt1 = product1$dataType
    timeCol1 = product1$timeCol
    priceCol1 = product1$priceCol
    symbol1 = product1$symbol

    mkt2 = productList2$marketType
    dt2 = productList2$dataType
    timeCol2 = productList2$timeCol
    priceCol2 = productList2$priceCol
    symbol2 = productList2$symbol

    begin = MakeTime(begin)
    end = MakeTime(end)

    data1 = dataSource1$ReadData(mkt1, dt1, symbol1, begin, end)
    data1 = FilterData(dataSource1, data1)
    data2 = dataSource2$ReadData(mkt2, dt2, symbol2, begin, end)
    data2 = FilterData(dataSource2, data2)

    time1 = data1[, get(timeCol1)]
    time2 = data2[, get(timeCol2)]

    time1BeginTd = GetTradingDay(first(time1), dataSource1, mkt1)
    time1EndTd = GetTradingDay(last(time1), dataSource1, mkt1)

    time2BeginTd = GetTradingDay(first(time2), dataSource2, mkt2)
    time2EndTd = GetTradingDay(last(time2), dataSource2, mkt2)

    if (time1BeginTd != time2BeginTd || time1EndTd != time2EndTd) {
        product1 = paste0(mkt1, "/", dt1, "/", symbol1)
        product2 = paste0(mkt2, "/", dt2, "/", symbol2)

        WarningLog("GetBasis", "The duration of market data between two prodcuts are not euqal:\n",
            product1, ": ", DateToStr(first(time1)), " - ", DateToStr(last(time1)), " \n",
            product2, ": ", DateToStr(first(time2)), " - ", DateToStr(last(time2)))
    }

    timeDiff1 = abs(difftime(time1[1], time1[2], units = "secs"))
    timeDiff2 = abs(difftime(time2[1], time2[2], units = "secs"))

    px1 = data1[, get(priceCol1)]
    px2 = data2[, get(priceCol2)]

    if (timeDiff1 < timeCol2) {
        px1 = SelectValue(time2, time1, px1, first(px1))
        commonTimestamp = time2
    } else {
        px2 = SelectValue(time1, time2, px2, first(px2))
        commonTimestamp = time2
    }

    diff = px1 - px2
    basis = diff / px2
    result = data.table(timestamp = commonTimestamp, diff = diff, basis = basis)

    class(result) = c(class(result), "Basis")
    return(result)
}