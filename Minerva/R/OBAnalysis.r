#' nodoc
#' @export
DrawOrderbookPNGDefaultFilePathFunc = function(rootPath, symbol, tradingDay) {
    ret = file.path(rootPath, paste0(tradingDay, ".png"))
    return(ret)
}

#' nodoc
#' @export
DrawOrderbookPNG = function(dataSource, marketType, dataType, symbol, begin, end, filePathRoot,
                              splitInterval = hours(1),
                              filePathFunc = DrawOrderbookPNGDefaultFilePathFunc,
                              filterData = TRUE) {

    data = dataSource$ReadData(marketType, dataType, symbol, begin, end)
    if (is.null(data)) return(FALSE)

    PushMaxPlotWidth(3e4)

    contractSize = GetContractSize(marketType, symbol)
    tickSize = GetTickSize(marketType, symbol)

    data$lastvol = TotalVolToLastVol(data, dataSource)
    data$lastval = TotalValueToLastValue(data)
    data[, midpx := 0.5 * (askpx0 + bidpx0)]
    data[, avglastpx := ifelse(lastvol == 0, lastpx, lastval / (lastvol * contractSize))]
    if (filterData) data = Demeter::FilterData(dataSource, data)


    data[, td := GetTradingDay(exchtime, dataSource, marketType)]
    res = 300
    maxInchLength = ifelse(IsLinux(), 100, 200)

    ListData = split(data, by = c("td"))
    for (td in names(ListData)) {
        tdData = ListData[[td]]

        pngList = list()
        start = first(tdData$exchtime)
        endTime = last(tdData$exchtime)

        while (start + splitInterval <= endTime) {
            b = start
            e = start + splitInterval
            myData = tdData[exchtime >= b & exchtime <= e]
            start = e
            if (nrow(myData) <= 1) next

            expWidth = 3 * nrow(myData)
            priceRange = (max(myData$lastpx) - min(myData$lastpx)) / tickSize
            expHeight = 20 * priceRange + 1000

            widthInInch = min(maxInchLength, ceiling(expWidth / res))
            heightInInch = min(maxInchLength, ceiling(expHeight / res))

            pngList[[length(pngList) + 1]] = list(data = myData,
                                                    width = widthInInch,
                                                    height = heightInInch)
        }

        stichedHeight = max(sapply(pngList, function(x) x$height))
        stichedPNGVec = c()
        tempFilePath = tempdir()

        for (i in seq_along(pngList)) {

            data = pngList[[i]]$data
            width = pngList[[i]]$width

            g = DrawOrderbook(data, tickSize)

            # add price text every 2000 pixels
            outputWidth = width * res
            step = ceiling(2000 * nrow(data) / outputWidth)
            if (nrow(data) >= 2 * step) {
                x = seq(step, nrow(data) - step, by = step)
                x = sapply(x, function(t) {
                    range = (t - 100):(t + 100)
                    range[which.max(data$lastpx[range])]
                })
                y = data$lastpx[x]
                g = g + geom_text(aes(x, y), label = signif(y, 5), hjust = -0.1,
                                  vjust = -0.5, family = "mono", size = 3,
                                  colour = "red", fontface = "bold", alpha = 0.2)
            }

            pngFilePath = file.path(tempFilePath, paste0(td, "_", i, ".png"))
            png(pngFilePath, width, stichedHeight, unit = "in", res = res)
            plot(g)
            dev.off()

            if (is.null(stichedPNGVec))
                stichedPNGVec = image_read(pngFilePath)
            else
                stichedPNGVec = c(stichedPNGVec, image_read(pngFilePath))

            InfoLog("DrawOrderbookPNG", "Drawing image: {i}/{length(pngList)}")
        }

        stichedPNGPath = filePathFunc(filePathRoot, symbol, td)
        dir.create(dirname(stichedPNGPath), FALSE, TRUE)

        InfoLog("DrawOrderbookPNG", "Stiching image")

        image_write(image_append(stichedPNGVec),
                    path = stichedPNGPath,
                    format = "png")

        InfoLog("DrawOrderbookPNG", "Output {symbol} Orderbook to {stichedPNGPath}")
    }

    PopMaxPlotWidth()
    return(TRUE)
}