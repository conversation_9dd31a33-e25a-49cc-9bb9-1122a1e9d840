suppressMessages(library(Iris))
suppressMessages(library(Demeter))
options(future.rng.onMisuse = "ignore")
SourceDir("/home/<USER>/projects/Aegis/Iris/R/")
needSleep = FALSE
sleepSec = 2
useRealPlot = FALSE
useListPriority = TRUE
# load("~/VMHome/temp/test.obj")
# InitLogger(threshold = "TRACE")
if (TRUE) { # define targets.
    t1 = NewMonitorTarget(group = "G1", subgroup = "g1",
        func = function() {
            if(needSleep) {
                Sys.sleep(sleepSec)
            }
            time = Sys.time()
            sec = format(time, format = "%S")
            # message("sec is ", sec)
            if (useRealPlot) {
                plot = x
            } else {
                plot=ggplot() + geom_point(aes(x = 10, y = as.integer(sec)))
            }
            if (useListPriority) {
                priority = list(pa = 0.86, pb = 0.76, pc = 0.98)
            } else {
                priority = 0.86
            }
            return(list(plot = plot, priority = priority))
        },
        session = data.table(
            begin = MakeTime("2025-05-29 00:00:00"),
            end = MakeTime("2025-05-29 23:55:00")
        ),
        interval = 19
    )

    t2 = NewMonitorTarget(group = "G1", subgroup = "g2",
        func = function() {
            if(needSleep) {
                Sys.sleep(sleepSec)
            }
            time = Sys.time()
            sec = format(time, format = "%S")
            if (useRealPlot) {
                plot = x
            } else {
                plot=ggplot() + geom_point(aes(x = 9, y = as.integer(sec)))
            }
            if (useListPriority) {
                priority = list(pa = 0.9, pb = 0.76, pc = 0.12)
            } else {
                priority = 0.9
            }
            return(list(plot = plot, priority = priority))
        },
        session = data.table(
            begin = MakeTime("2025-05-29 00:00:00"),
            end = MakeTime("2025-05-29 23:55:00")
        ),
        interval = 3
    )

    t3 = NewMonitorTarget(group = "G2", subgroup = "g3",
        func = function() {
            if(needSleep) {
                Sys.sleep(sleepSec)
            }
            time = Sys.time()
            sec = format(time, format = "%S")
            priority = as.integer(sec) / 60
            TraceLog("monitor", "T3 priority: ", priority)
            if (useRealPlot) {
                plot = x
            } else {
                plot = ggplot() + geom_point(aes(x = 10, y = as.integer(sec)))
            }
            if (useListPriority) {
                vp = ifelse(as.POSIXlt(Sys.time())$sec > 30, 0.29, 0.93)
                priority = list(pa = priority, pb = 0.88, pc = vp)
            } else {
                priority = priority
            }
            return(list(plot = plot, priority = priority))
        },
        session = data.table(
            begin = MakeTime("2025-05-29 00:00:00"),
            end = MakeTime("2025-05-29 23:55:00")
        ),
        interval = 18
    )

    t4 = NewMonitorTarget(group = "G2", subgroup = "g1",
        func = function() {
            if(needSleep) {
                Sys.sleep(sleepSec)
            }
            time = Sys.time()
            sec = format(time, format = "%S")
            if (useRealPlot) {
                plot = x
            } else {
                plot = ggplot() + geom_point(aes(x = 11, y = as.integer(sec)))
            }
            if (useListPriority) {
                vp = ifelse(as.POSIXlt(Sys.time())$sec > 30, 0.9, 0.3)
                priority = list(pa = 0.5, pb = 0.7, pc = vp)
            } else {
                priority = 0.5
            }
            return(list(plot = plot, priority = priority))
        },
        session = data.table(
            begin = MakeTime("2025-05-29 00:00:00"),
            end = MakeTime("2025-05-29 23:55:00")
        ),
        interval = 2
    )

    t5 = NewMonitorTarget(group = "G2", subgroup = "g3", # "g4",
        func = function() {
            if(needSleep) {
                Sys.sleep(sleepSec)
            }
            time = Sys.time()
            sec = format(time, format = "%S")
            if (useRealPlot) {
                plot = x
            } else {
                plot = ggplot() + geom_point(aes(x = 10, y = as.integer(sec)))
            }
            if (useListPriority) {
                vp = ifelse(as.POSIXlt(Sys.time())$sec > 30, 0.9, 0.3)
                priority = list(pa = 0.79, pb = vp, pc = 0.12)
            } else {
                priority = 0.79
            }
            return(list(plot = plot, priority = priority))
        },
        session = data.table(
            begin = MakeTime("2025-05-29 00:00:00"),
            end = MakeTime("2025-05-29 23:55:00")
        ),
        interval = 10
    )

    t6 = NewMonitorTarget(group = "G2", subgroup = "g1", # "g5", # "g1",
        func = function() {
            if(needSleep) {
                Sys.sleep(sleepSec)
            }
            time = Sys.time()
            sec = format(time, format = "%S")
            if (useRealPlot) {
                plot = x
            } else {
                plot = ggplot() + geom_point(aes(x = 9, y = as.integer(sec)))
            }
            if (useListPriority) {
                vp = ifelse(as.POSIXlt(Sys.time())$sec > 30, 0.29, 0.93)
                priority = list(pa = 0.89, pb = 0.89, pc = vp, ph = NA)
            } else {
                priority = 0.89
            }
            return(list(plot = plot, priority = priority))
        },
        session = data.table(
            begin = MakeTime("2025-05-29 00:00:00"),
            end = MakeTime("2025-05-29 23:55:00")
        ),
        interval = 9
    )

    t7 = NewMonitorTarget(group = "G1", subgroup = "g2", # "g6", # "g2",
        func = function() {
            if(needSleep) {
                Sys.sleep(sleepSec)
            }
            time = Sys.time()
            sec = format(time, format = "%S")
            if (useRealPlot) {
                plot = x
            } else {
                plot = ggplot() + geom_point(aes(x = 10, y = as.integer(sec)))
            }
            if (useListPriority) {
                vp = ifelse(as.POSIXlt(Sys.time())$sec > 30, 0.19, 0.93)
                priority = list(pa = 0.67, pb = 0.87, pc = vp, pg = NaN)
            } else {
                priority = 0.67
            }
            return(list(plot = plot, priority = priority))
        },
        session = data.table(
            begin = MakeTime("2025-05-29 00:00:00"),
            end = MakeTime("2025-05-29 23:55:00")
        ),
        interval = 9
    )

    t8 = NewMonitorTarget(group = "G1", subgroup = "g1", # "g7", # "g1",
        func = function() {
            if(needSleep) {
                Sys.sleep(sleepSec)
            }
            time = Sys.time()
            sec = format(time, format = "%S")
            if (useRealPlot) {
                plot = x
            } else {
                plot = ggplot() + geom_point(aes(x = 9, y = as.integer(sec)))
            }
            if (useListPriority) {
                vp = ifelse(as.POSIXlt(Sys.time())$sec > 30, 0.9, 0.3)
                priority = list(pa = 0.59, pb = vp, pc = 0.89, pf = Inf)
            } else {
                priority = 0.59
            }
            return(list(plot = plot, priority = priority))
        },
        session = data.table(
            begin = MakeTime("2025-05-29 00:00:00"),
            end = MakeTime("2025-05-29 23:55:00")
        ),
        interval = 11
    )

    t9 = NewMonitorTarget(group = "G1", subgroup = "g4",
        func = function() {
            if(needSleep) {
                Sys.sleep(sleepSec)
            }
            time = Sys.time()
            sec = format(time, format = "%S")
            if (useRealPlot) {
                plot = x
            } else {
                plot = ggplot() + geom_point(aes(x = 10, y = as.integer(sec)))
            }
            if (FALSE) {
                vp = ifelse(as.POSIXlt(Sys.time())$sec > 30, 0.9, 0.3)
                priority = list(0.1, pa = vp, pb = 0.75, pc = 0.78, pe = c(0.87, 0.88))
            } else {
                priority = 0.9
            }
            return(list(plot = plot, priority = priority))
        },
        session = data.table(
            begin = MakeTime("2025-05-29 00:00:00"),
            end = MakeTime("2025-05-29 23:55:00")
        ),
        interval = 9
    )

    t10 = NewMonitorTarget(group = "G1", subgroup = "g5",
        func = function() {
            if(needSleep) {
                Sys.sleep(sleepSec)
            }
            time = Sys.time()
            sec = format(time, format = "%S")
            if (useRealPlot) {
                plot = x
            } else {
                plot = ggplot() + geom_point(aes(x = 10, y = as.integer(sec)))
            }
            if (useListPriority) {
                vp = ifelse(as.POSIXlt(Sys.time())$sec > 30, 0.9, 0.3)
                priority = list(pa = 0.29, pb = 0.78, pc = 0.87, aa = vp, pd = "0.89")
            } else {
                priority = 0.19
            }
            return(list(plot = plot, priority = priority))
        },
        session = data.table(
            begin = MakeTime("2025-05-29 00:00:00"),
            end = MakeTime("2025-05-29 23:55:00")
        ),
        interval = 9
    )


    targets = list(t1, t2, t3, t4, t5, t6, t7, t8, t1, t2, t3, t4, t5, t6, t7, t8, t9, t1, t2, t3, t4, t5, t6, t7, t8, t9, t1, t1, t1, t2,
                    t1, t2, t3, t4, t5, t6, t7, t8, t1, t2, t3, t4, t5, t6, t7, t8, t9, t1, t2, t3, t4, t5, t6, t7, t8, t9, t1, t1, t1, t2,
                    t1, t2, t3, t4, t5, t6, t7, t8, t1, t2, t3, t4, t5, t6, t7, t8, t9, t1, t2, t3, t4, t5, t6, t7, t8, t9, t1, t1, t1, t2,
                    t1, t2, t3, t4, t5, t6, t7, t8, t1, t2, t3, t4, t5, t6, t7, t8, t9, t1, t2, t3, t4, t5, t6, t7, t8, t9, t1, t1, t1, t2,
                    t1, t2, t3, t4, t5, t6, t7, t8, t1, t2, t3, t4, t5, t6, t7, t8, t9, t1, t2, t3, t10, t9)
} else {

    t1 = NewMonitorTarget(group = "G1", subgroup = "g1",
        func = function() {
            plot=ggplot() + geom_point(aes(x = 1, y = 1))
            priority = list(pa = 1, pd = 0.99)
            return(list(plot = plot, priority = priority))
        },
        session = data.table(
            begin = MakeTime("2025-05-29 00:00:00"),
            end = MakeTime("2025-05-29 23:55:00")
        ),
        interval = 19
    )


    t2 = NewMonitorTarget(group = "G1", subgroup = "g2",
        func = function() {
            plot=ggplot() + geom_point(aes(x = 2, y = 2))
            priority = list(pa = 0.86, pb = 0.86, pc = 0.98, pd = 0.99)
            return(list(plot = plot, priority = priority))
        },
        session = data.table(
            begin = MakeTime("2025-05-29 00:00:00"),
            end = MakeTime("2025-05-29 23:55:00")
        ),
        interval = 19
    )


    t3 = NewMonitorTarget(group = "G3", subgroup = "g3",
        func = function() {
            plot=ggplot() + geom_point(aes(x = 3, y = 3))
            priority = list(pa = 0.86, pb = 0.16, pc = 1, pd = 0.99)
            return(list(plot = plot, priority = priority))
        },
        session = data.table(
            begin = MakeTime("2025-05-29 00:00:00"),
            end = MakeTime("2025-05-29 23:55:00")
        ),
        interval = 19
    )

    t4 = NewMonitorTarget(group = "G3", subgroup = "g3",
        func = function() {
            plot=ggplot() + geom_point(aes(x = 4, y = 4))
            vp = ifelse(as.POSIXlt(Sys.time())$sec > 30, 0.9, 0.3)
            priority = list(pa = 0.86, pb = 0.16, pc = 1, pd = vp)
            return(list(plot = plot, priority = priority))
        },
        session = data.table(
            begin = MakeTime("2025-05-29 00:00:00"),
            end = MakeTime("2025-05-29 23:55:00")
        ),
        interval = 19
    )

    t5 = NewMonitorTarget(group = "G3", subgroup = "g3",
        func = function() {
            plot=ggplot() + geom_point(aes(x = 5, y = 5))
            priority = list(pa = 0.86, pb = 0.16, pc = 1, pd = 0.99)
            return(list(plot = plot, priority = priority))
        },
        session = data.table(
            begin = MakeTime("2025-05-29 00:00:00"),
            end = MakeTime("2025-05-29 23:55:00")
        ),
        interval = 19
    )

    t6 = NewMonitorTarget(group = "G3", subgroup = "g3",
        func = function() {
            plot=ggplot() + geom_point(aes(x = 6, y = 6))
            priority = list(pa = 0.86, pb = 0.16, pc = 1, pd = 0.99)
            return(list(plot = plot, priority = priority))
        },
        session = data.table(
            begin = MakeTime("2025-05-29 00:00:00"),
            end = MakeTime("2025-05-29 23:55:00")
        ),
        interval = 19
    )

    targets = list(t1, t2, t3, t4, t5, t6)
}


ncol = 2
monitorLayout = data.table(group = "*", layout = ncol)

monitor = NewMarketMonitor(
    "Monitor",
    56090,
    targets = targets,
    list(width = 2560, height = 1294, maxPlotHeight = 500),
    6,
    monitorLayout,
    nthreads = 10,
    refreshInterval = 10,
    # priorityTypes = c("pa", "pb", "pc", "aa", "Swing", "Bias", "MajorOI", "MajorVol", "Liquidity", "FMA", "Manual"),
    priorityTypes = c("pa", "pb", "pc", "pd"),
    maxAutoRefreshNumber = 10,
    maxImportantRatio = 1
)

StartMarketMonitor(monitor)
