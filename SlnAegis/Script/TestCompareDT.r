suppressMessages(library(Demeter))
suppressMessages(library(Hades))
SetPreferredConcurrency()
InitDemeter()
InitRUtil()

componentName = "TestCompareDT"
InitLogger(GenLogPath("~/Log", componentName))
source("/home/<USER>/VMHome/projects/Aegis/Hades/R/CheckDataDailyUpdate.r")

marketType = "ChinaStock"
dataType = "Misc/TradingDay"
destDS = GetCSVDataSource("~/RawData/MarketData/Standard")
srcDS = GetCSVDataSource("~/VMHomeExt/RawData/MarketData/Standard")
# result = CheckDataEqual(marketType, dataType, srcDS, destDS, begin = MakeTime("2025-05-19"), end = MakeTime("2025-05-20"), verbose = TRUE, symbols = c("600000", "688819", "688981"))
result = CheckDataEqual(marketType, dataType, srcDS, destDS, verbose = TRUE, symbols = c("600000"))
InfoLog(componentName, "result: {result}")